<?php
/**
 * Test Field Labels Specifically
 * 
 * This script tests if field labels are being retrieved correctly from form definition
 */

// WordPress environment
require_once('../../../wp-config.php');

echo "<h1>CFB Calculator Field Labels Test</h1>";

global $wpdb;

// Test 1: Check what's in the forms table
echo "<h2>1. Form Definition Analysis</h2>";

$forms = $wpdb->get_results("SELECT id, name, form_data FROM {$wpdb->prefix}cfb_forms LIMIT 3");

foreach ($forms as $form) {
    echo "<h3>Form ID: {$form->id} - {$form->name}</h3>";
    
    if ($form->form_data) {
        $form_data = json_decode($form->form_data, true);
        
        if (isset($form_data['fields']) && is_array($form_data['fields'])) {
            echo "<p><strong>Fields found:</strong> " . count($form_data['fields']) . "</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field Name</th><th>Field Label</th><th>Field Type</th></tr>";
            
            foreach ($form_data['fields'] as $field) {
                $name = $field['name'] ?? 'N/A';
                $label = $field['label'] ?? 'N/A';
                $type = $field['type'] ?? 'N/A';
                echo "<tr><td>{$name}</td><td>{$label}</td><td>{$type}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ No fields array found in form_data</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No form_data found</p>";
    }
}

// Test 2: Test the PDF generator's field label method
echo "<h2>2. PDF Generator Field Label Method Test</h2>";

if (class_exists('CFB_PDF_Generator')) {
    $pdf_generator = CFB_PDF_Generator::get_instance();
    
    // Use reflection to access private method
    $reflection = new ReflectionClass($pdf_generator);
    $method = $reflection->getMethod('get_form_field_labels');
    $method->setAccessible(true);
    
    foreach ($forms as $form) {
        echo "<h3>Testing Form ID: {$form->id}</h3>";
        $field_labels = $method->invoke($pdf_generator, $form->id);
        
        if (!empty($field_labels)) {
            echo "<p style='color: green;'>✅ Field labels retrieved successfully</p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field Name</th><th>Field Label</th></tr>";
            foreach ($field_labels as $name => $label) {
                echo "<tr><td>{$name}</td><td>{$label}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: red;'>❌ No field labels retrieved</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ CFB_PDF_Generator class not found</p>";
}

// Test 3: Test with actual submission data
echo "<h2>3. Submission Data vs Field Labels Test</h2>";

$submissions = $wpdb->get_results("SELECT id, form_id, submission_data FROM {$wpdb->prefix}cfb_form_submissions ORDER BY id DESC LIMIT 3");

foreach ($submissions as $submission) {
    echo "<h3>Submission ID: {$submission->id} (Form ID: {$submission->form_id})</h3>";
    
    if ($submission->submission_data) {
        $submission_data = json_decode($submission->submission_data, true);
        
        // Get field labels for this form
        if (class_exists('CFB_PDF_Generator')) {
            $pdf_generator = CFB_PDF_Generator::get_instance();
            $reflection = new ReflectionClass($pdf_generator);
            $method = $reflection->getMethod('get_form_field_labels');
            $method->setAccessible(true);
            $field_labels = $method->invoke($pdf_generator, $submission->form_id);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Field Name</th><th>Field Label</th><th>Submitted Value</th></tr>";
            
            foreach ($submission_data as $field_name => $field_value) {
                // Skip system fields
                if (in_array($field_name, array('nonce', 'action', '_wp_http_referer', 'want_invoice', 'invoice_name', 'invoice_email', 'invoice_phone', 'invoice_address'))) {
                    continue;
                }
                
                $field_label = isset($field_labels[$field_name]) ? $field_labels[$field_name] : 'NO LABEL FOUND';
                $display_value = is_array($field_value) ? implode(', ', $field_value) : $field_value;
                
                $label_color = ($field_label === 'NO LABEL FOUND') ? 'color: red;' : 'color: green;';
                
                echo "<tr>";
                echo "<td>{$field_name}</td>";
                echo "<td style='{$label_color}'>{$field_label}</td>";
                echo "<td>{$display_value}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
}

echo "<h2>4. Summary</h2>";
echo "<p>This test shows exactly what field labels are available and how they match with submission data.</p>";
echo "<p>If field labels show 'NO LABEL FOUND', then the PDF will show empty labels.</p>";

?>
