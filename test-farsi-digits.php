<?php
/**
 * Test Farsi Digits Function
 */

require_once('../../../wp-config.php');

echo "=== TESTING FARSI DIGITS FUNCTION ===\n";

if (class_exists('CFB_PDF_Generator')) {
    $pdf_generator = CFB_PDF_Generator::get_instance();
    $reflection = new ReflectionClass($pdf_generator);
    $method = $reflection->getMethod('convert_to_farsi_digits');
    $method->setAccessible(true);
    
    $test_strings = array(
        'کاغذ داخلی',
        'تیراژ',
        'تعداد صفحات',
        'جمع کل',
        'Dropdown',
        '130',
        '1000',
        'Test 123 String',
        'Persian ۱۲۳ Text'
    );
    
    echo "Testing convert_to_farsi_digits function:\n";
    foreach ($test_strings as $test) {
        $result = $method->invoke($pdf_generator, $test);
        echo "'{$test}' => '{$result}'\n";
    }
    
    echo "\n=== TESTING ACTUAL PDF FIELD DISPLAY ===\n";
    
    // Get the latest invoice
    global $wpdb;
    $invoice = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_invoices ORDER BY id DESC LIMIT 1");
    
    if ($invoice && $invoice->form_data) {
        $form_data = json_decode($invoice->form_data, true);
        
        // Get field labels
        $field_labels_method = $reflection->getMethod('get_form_field_labels');
        $field_labels_method->setAccessible(true);
        $field_labels = $field_labels_method->invoke($pdf_generator, $invoice->form_id);
        
        echo "Form ID: {$invoice->form_id}\n";
        echo "Field labels retrieved: " . count($field_labels) . "\n\n";
        
        foreach ($form_data as $field_name => $field_value) {
            // Skip system fields
            if (in_array($field_name, array('nonce', 'action', '_wp_http_referer', 'customer_name', 'customer_email', 'customer_phone', 'customer_address', 'invoice_name', 'invoice_email', 'invoice_phone', 'invoice_address'))) {
                continue;
            }
            
            $clean_field_name = isset($field_labels[$field_name]) ? $field_labels[$field_name] : ucwords(str_replace(['_', '-'], ' ', $field_name));
            $converted_field_name = $method->invoke($pdf_generator, $clean_field_name);
            $converted_field_value = $method->invoke($pdf_generator, $field_value);
            
            echo "Field: {$field_name}\n";
            echo "  Original Label: '{$clean_field_name}'\n";
            echo "  Converted Label: '{$converted_field_name}'\n";
            echo "  Original Value: '{$field_value}'\n";
            echo "  Converted Value: '{$converted_field_value}'\n";
            echo "  Label Empty? " . (empty($converted_field_name) ? 'YES' : 'NO') . "\n";
            echo "---\n";
        }
    }
} else {
    echo "CFB_PDF_Generator class not found\n";
}

?>
