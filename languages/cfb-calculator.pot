# CFB Calculator Language Template
# Copyright (C) 2024 CFB Team
# This file is distributed under the same license as the CFB Calculator package.
msgid ""
msgstr ""
"Project-Id-Version: CFB Calculator 1.0.7\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-01-01 00:00+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: cfb-calculator.php:249
#: cfb-calculator.php:250
msgid "CFB Calculator"
msgstr ""

#: cfb-calculator.php:260
#: cfb-calculator.php:261
msgid "Dashboard"
msgstr ""

#: cfb-calculator.php:269
#: cfb-calculator.php:270
msgid "Invoices"
msgstr ""

#: cfb-calculator.php:278
#: cfb-calculator.php:279
msgid "All Forms"
msgstr ""

#: cfb-calculator.php:287
#: cfb-calculator.php:288
msgid "Add New Form"
msgstr ""

#: cfb-calculator.php:296
#: cfb-calculator.php:297
msgid "Variables"
msgstr ""

#: cfb-calculator.php:305
#: cfb-calculator.php:306
msgid "Settings"
msgstr ""

#: cfb-calculator.php:314
msgid "AI Settings & Formula Builder"
msgstr ""

#: cfb-calculator.php:315
msgid "AI Settings"
msgstr ""

#: cfb-calculator.php:378
msgid "Unauthorized access"
msgstr ""

#: includes/class-cfb-admin.php:45
msgid "CFB Calculator Forms"
msgstr ""

#: includes/class-cfb-admin.php:46
msgid "Add New"
msgstr ""

#: includes/class-cfb-admin.php:53
msgid "No forms found"
msgstr ""

#: includes/class-cfb-admin.php:54
msgid "Create your first price calculation form to get started."
msgstr ""

#: includes/class-cfb-admin.php:56
msgid "Create Your First Form"
msgstr ""

#: includes/class-cfb-admin.php:125
msgid "Active"
msgstr ""

#: includes/class-cfb-admin.php:125
msgid "Inactive"
msgstr ""

#: includes/class-cfb-admin.php:129
msgid "%d fields"
msgstr ""

#: includes/class-cfb-admin.php:130
msgid "%d submissions"
msgstr ""

#: includes/class-cfb-admin.php:134
msgid "Edit"
msgstr ""

#: includes/class-cfb-admin.php:137
msgid "Duplicate"
msgstr ""

#: includes/class-cfb-admin.php:140
msgid "Deactivate"
msgstr ""

#: includes/class-cfb-admin.php:140
msgid "Activate"
msgstr ""

#: includes/class-cfb-admin.php:143
msgid "Delete"
msgstr ""

#: includes/class-cfb-admin.php:146
msgid "Click to copy shortcode"
msgstr ""

#: includes/class-cfb-admin.php:152
msgid "Created: %s"
msgstr ""

#: includes/class-cfb-admin.php:155
msgid "Updated: %s"
msgstr ""

#: includes/class-cfb-form-builder.php:25
msgid "Text Field"
msgstr ""

#: includes/class-cfb-form-builder.php:29
msgid "Number Field"
msgstr ""

#: includes/class-cfb-form-builder.php:33
msgid "Slider"
msgstr ""

#: includes/class-cfb-form-builder.php:37
msgid "Dropdown"
msgstr ""

#: includes/class-cfb-form-builder.php:41
msgid "Radio Buttons"
msgstr ""

#: includes/class-cfb-form-builder.php:45
msgid "Checkboxes"
msgstr ""

#: includes/class-cfb-form-builder.php:85
msgid "Select an option"
msgstr ""

#: includes/class-cfb-form-builder.php:88
#: includes/class-cfb-form-builder.php:100
#: includes/class-cfb-form-builder.php:112
msgid "Option 1"
msgstr ""

#: includes/class-cfb-form-builder.php:89
#: includes/class-cfb-form-builder.php:101
#: includes/class-cfb-form-builder.php:113
msgid "Option 2"
msgstr ""

#: includes/class-cfb-form-builder.php:140
msgid "Form name is required"
msgstr ""

#: includes/class-cfb-form-builder.php:157
msgid "Form saved successfully"
msgstr ""

#: includes/class-cfb-form-builder.php:160
msgid "Failed to save form"
msgstr ""

#: includes/class-cfb-form-builder.php:175
msgid "Edit Form"
msgstr ""

#: includes/class-cfb-form-builder.php:175
msgid "Create New Form"
msgstr ""

#: includes/class-cfb-form-builder.php:180
msgid "Form Settings"
msgstr ""

#: includes/class-cfb-form-builder.php:183
msgid "Form Name"
msgstr ""

#: includes/class-cfb-form-builder.php:184
msgid "Enter form name"
msgstr ""

#: includes/class-cfb-form-builder.php:188
msgid "Description"
msgstr ""

#: includes/class-cfb-form-builder.php:189
msgid "Enter form description"
msgstr ""

#: includes/class-cfb-form-builder.php:193
msgid "Calculation Settings"
msgstr ""

#: includes/class-cfb-form-builder.php:197
msgid "Enable Subtotals"
msgstr ""

#: includes/class-cfb-form-builder.php:201
msgid "Save Submissions"
msgstr ""

#: includes/class-cfb-form-builder.php:207
msgid "Field Types"
msgstr ""

#: includes/class-cfb-form-builder.php:218
msgid "Form Fields"
msgstr ""

#: includes/class-cfb-form-builder.php:227
msgid "Drag field types here to build your form"
msgstr ""

#: includes/class-cfb-form-builder.php:232
msgid "Calculation Configuration"
msgstr ""

#: includes/class-cfb-form-builder.php:235
msgid "Subtotals"
msgstr ""

#: includes/class-cfb-form-builder.php:242
msgid "Add Subtotal"
msgstr ""

#: includes/class-cfb-form-builder.php:246
msgid "Total Formula"
msgstr ""

#: includes/class-cfb-form-builder.php:247
msgid "Enter total formula (e.g., subtotal_1 + subtotal_2)"
msgstr ""

#: includes/class-cfb-form-builder.php:248
msgid "Use field names in {brackets} and subtotal_1, subtotal_2, etc. for subtotals. Supports: +, -, *, /, ceil(), floor(), min(), max(), pow(), if()"
msgstr ""

#: includes/class-cfb-form-builder.php:254
msgid "Save Form"
msgstr ""

#: includes/class-cfb-form-builder.php:255
msgid "Preview"
msgstr ""

#: includes/class-cfb-form-builder.php:256
msgid "Back to Forms"
msgstr ""

#: includes/class-cfb-form-builder.php:264
msgid "Edit Field"
msgstr ""

#: includes/class-cfb-form-builder.php:267
msgid "Delete Field"
msgstr ""

#: includes/class-cfb-form-builder.php:284
msgid "Subtotal Label"
msgstr ""

#: includes/class-cfb-form-builder.php:285
msgid "Enter subtotal label"
msgstr ""

#: includes/class-cfb-form-builder.php:288
msgid "Formula"
msgstr ""

#: includes/class-cfb-form-builder.php:289
msgid "Enter formula"
msgstr ""

#: includes/class-cfb-form-builder.php:291
msgid "Remove"
msgstr ""

#: includes/class-cfb-frontend.php:25
msgid "Invalid form ID"
msgstr ""

#: includes/class-cfb-frontend.php:30
msgid "Form not found"
msgstr ""

#: includes/class-cfb-frontend.php:55
msgid "Breakdown"
msgstr ""

#: includes/class-cfb-frontend.php:66
msgid "Total"
msgstr ""

#: includes/class-cfb-frontend.php:73
msgid "Calculate"
msgstr ""

#: includes/class-cfb-frontend.php:74
msgid "Reset"
msgstr ""

#: includes/class-cfb-frontend.php:78
msgid "Calculating..."
msgstr ""

#: includes/class-cfb-formula-engine.php:50
msgid "Invalid form data"
msgstr ""

#: includes/class-cfb-formula-engine.php:56
msgid "Form not found"
msgstr ""

#: includes/class-cfb-settings.php:45
msgid "CFB Calculator Settings"
msgstr ""

#: includes/class-cfb-settings.php:50
msgid "Currency"
msgstr ""

#: includes/class-cfb-settings.php:54
msgid "Display"
msgstr ""

#: includes/class-cfb-settings.php:58
msgid "Language"
msgstr ""

#: includes/class-cfb-settings.php:62
msgid "Advanced"
msgstr ""

#: includes/class-cfb-settings.php:70
msgid "Currency Settings"
msgstr ""

#: includes/class-cfb-settings.php:74
msgid "Currency Symbol"
msgstr ""

#: includes/class-cfb-settings.php:80
msgid "The symbol to display for currency (e.g., $, €, £, ﷼)"
msgstr ""

#: includes/class-cfb-settings.php:85
msgid "Currency Position"
msgstr ""

#: includes/class-cfb-settings.php:89
msgid "Left ($100)"
msgstr ""

#: includes/class-cfb-settings.php:92
msgid "Right (100$)"
msgstr ""

#: includes/class-cfb-settings.php:95
msgid "Position of currency symbol relative to the amount"
msgstr ""

#: includes/class-cfb-settings.php:100
msgid "Decimal Places"
msgstr ""

#: includes/class-cfb-settings.php:108
msgid "Number of decimal places to display"
msgstr ""

#: includes/class-cfb-settings.php:113
msgid "Thousand Separator"
msgstr ""

#: includes/class-cfb-settings.php:119
msgid "Character used to separate thousands (e.g., comma, space)"
msgstr ""

#: includes/class-cfb-settings.php:124
msgid "Decimal Separator"
msgstr ""

#: includes/class-cfb-settings.php:130
msgid "Character used to separate decimal places"
msgstr ""

#: includes/class-cfb-settings.php:137
msgid "Display Settings"
msgstr ""

#: includes/class-cfb-settings.php:141
msgid "Default Theme"
msgstr ""

#: includes/class-cfb-settings.php:145
msgid "Modern"
msgstr ""

#: includes/class-cfb-settings.php:148
msgid "Classic"
msgstr ""

#: includes/class-cfb-settings.php:151
msgid "Minimal"
msgstr ""

#: includes/class-cfb-settings.php:154
msgid "Default theme for calculator forms"
msgstr ""

#: includes/class-cfb-settings.php:159
msgid "Enable Animations"
msgstr ""

#: includes/class-cfb-settings.php:166
msgid "Enable smooth animations and transitions"
msgstr ""

#: includes/class-cfb-settings.php:172
msgid "Auto Calculate"
msgstr ""

#: includes/class-cfb-settings.php:179
msgid "Calculate automatically when form values change"
msgstr ""

#: includes/class-cfb-settings.php:187
msgid "Language & RTL Settings"
msgstr ""

#: includes/class-cfb-settings.php:191
msgid "RTL Support"
msgstr ""

#: includes/class-cfb-settings.php:198
msgid "Enable Right-to-Left (RTL) language support"
msgstr ""

#: includes/class-cfb-settings.php:200
msgid "Automatically adjusts layout for RTL languages like Arabic, Hebrew, Farsi"
msgstr ""

#: includes/class-cfb-settings.php:205
msgid "Default Language"
msgstr ""

#: includes/class-cfb-settings.php:209
msgid "English"
msgstr ""

#: includes/class-cfb-settings.php:212
msgid "Farsi (Persian)"
msgstr ""

#: includes/class-cfb-settings.php:215
msgid "Arabic"
msgstr ""

#: includes/class-cfb-settings.php:218
msgid "Default language for new forms"
msgstr ""

#: includes/class-cfb-settings.php:226
msgid "Advanced Settings"
msgstr ""

#: includes/class-cfb-settings.php:230
msgid "Enable Caching"
msgstr ""

#: includes/class-cfb-settings.php:237
msgid "Cache calculation results for better performance"
msgstr ""

#: includes/class-cfb-settings.php:243
msgid "Debug Mode"
msgstr ""

#: includes/class-cfb-settings.php:250
msgid "Enable debug mode for troubleshooting"
msgstr ""

#: includes/class-cfb-settings.php:256
msgid "Custom CSS"
msgstr ""

#: includes/class-cfb-settings.php:263
msgid "Add custom CSS to style your calculator forms"
msgstr ""

#: includes/class-cfb-settings.php:268
msgid "Save Settings"
msgstr ""

#: includes/class-cfb-settings.php:269
msgid "Reset to Defaults"
msgstr ""

#: includes/class-cfb-settings.php:330
msgid "Settings saved successfully"
msgstr ""

# Dashboard strings
#: includes/class-cfb-dashboard.php:37
msgid "CFB Calculator Dashboard"
msgstr ""

#: includes/class-cfb-dashboard.php:48
msgid "Total Forms"
msgstr ""

#: includes/class-cfb-dashboard.php:58
msgid "Total Submissions"
msgstr ""

#: includes/class-cfb-dashboard.php:68
msgid "Total Revenue"
msgstr ""

#: includes/class-cfb-dashboard.php:78
msgid "Total Invoices"
msgstr ""

#: includes/class-cfb-dashboard.php:87
msgid "Submissions Over Time"
msgstr ""

#: includes/class-cfb-dashboard.php:96
msgid "Popular Forms"
msgstr ""

#: includes/class-cfb-dashboard.php:103
msgid "%d submissions"
msgstr ""

#: includes/class-cfb-dashboard.php:108
msgid "Edit"
msgstr ""

#: includes/class-cfb-dashboard.php:119
msgid "Recent Activity"
msgstr ""

#: includes/class-cfb-dashboard.php:122
msgid "No recent activity found."
msgstr ""

#: includes/class-cfb-dashboard.php:127
msgid "Form"
msgstr ""

#: includes/class-cfb-dashboard.php:128
msgid "Total"
msgstr ""

#: includes/class-cfb-dashboard.php:129
msgid "Date"
msgstr ""

#: includes/class-cfb-dashboard.php:130
msgid "Actions"
msgstr ""

#: includes/class-cfb-dashboard.php:144
msgid "Create Invoice"
msgstr ""

#: includes/class-cfb-dashboard.php:157
msgid "Quick Actions"
msgstr ""

#: includes/class-cfb-dashboard.php:161
msgid "Create New Form"
msgstr ""

#: includes/class-cfb-dashboard.php:165
msgid "Manage Invoices"
msgstr ""

#: includes/class-cfb-dashboard.php:169
msgid "Manage Variables"
msgstr ""

# Invoice strings
#: includes/class-cfb-invoices.php:59
msgid "Invoices"
msgstr ""

#: includes/class-cfb-invoices.php:62
msgid "Create New Invoice"
msgstr ""

#: includes/class-cfb-invoices.php:70
msgid "No invoices found"
msgstr ""

#: includes/class-cfb-invoices.php:71
msgid "Create your first invoice to get started."
msgstr ""

#: includes/class-cfb-invoices.php:73
msgid "Create Your First Invoice"
msgstr ""

#: includes/class-cfb-invoices.php:80
msgid "Invoice #"
msgstr ""

#: includes/class-cfb-invoices.php:81
msgid "Customer"
msgstr ""

#: includes/class-cfb-invoices.php:82
msgid "Form"
msgstr ""

#: includes/class-cfb-invoices.php:83
msgid "Total"
msgstr ""

#: includes/class-cfb-invoices.php:84
msgid "Status"
msgstr ""

#: includes/class-cfb-invoices.php:85
msgid "Date"
msgstr ""

#: includes/class-cfb-invoices.php:86
msgid "Actions"
msgstr ""

#: includes/class-cfb-invoices.php:112
msgid "View"
msgstr ""

#: includes/class-cfb-invoices.php:114
msgid "Edit"
msgstr ""

#: includes/class-cfb-invoices.php:117
msgid "PDF"
msgstr ""

#: includes/class-cfb-invoices.php:121
msgid "Delete"
msgstr ""

#: includes/class-cfb-invoices.php:204
msgid "Generating..."
msgstr ""

#: includes/class-cfb-invoices.php:226
msgid "PDF"
msgstr ""

#: includes/class-cfb-invoices.php:233
msgid "Are you sure you want to delete this invoice?"
msgstr ""

#: includes/class-cfb-invoices.php:290
msgid "Create New Invoice"
msgstr ""

#: includes/class-cfb-invoices.php:293
msgid "Back to Invoices"
msgstr ""

#: includes/class-cfb-invoices.php:304
msgid "Customer Information"
msgstr ""

#: includes/class-cfb-invoices.php:308
msgid "Customer Name"
msgstr ""

#: includes/class-cfb-invoices.php:316
msgid "Email Address"
msgstr ""

#: includes/class-cfb-invoices.php:324
msgid "Phone Number"
msgstr ""

#: includes/class-cfb-invoices.php:332
msgid "Address"
msgstr ""

#: includes/class-cfb-invoices.php:433
msgid "Security check failed"
msgstr ""

#: includes/class-cfb-invoices.php:441
msgid "Customer name and email are required"
msgstr ""

#: includes/class-cfb-invoices.php:473
msgid "Database error: "
msgstr ""

#: includes/class-cfb-invoices.php:499
msgid "Invoice created successfully"
msgstr ""

# Variables strings
#: includes/class-cfb-variables.php:50
msgid "Variables"
msgstr ""

#: includes/class-cfb-variables.php:55
msgid "Add New Variable"
msgstr ""

#: includes/class-cfb-variables.php:65
msgid "No variables found"
msgstr ""

#: includes/class-cfb-variables.php:66
msgid "Create your first variable to get started."
msgstr ""

#: includes/class-cfb-variables.php:68
msgid "Create Your First Variable"
msgstr ""

#: includes/class-cfb-variables.php:75
msgid "Variable Name"
msgstr ""

#: includes/class-cfb-variables.php:76
msgid "Label"
msgstr ""

#: includes/class-cfb-variables.php:77
msgid "Value"
msgstr ""

#: includes/class-cfb-variables.php:78
msgid "Category"
msgstr ""

#: includes/class-cfb-variables.php:79
msgid "Status"
msgstr ""

#: includes/class-cfb-variables.php:80
msgid "Actions"
msgstr ""

#: includes/class-cfb-variables.php:95
msgid "Active"
msgstr ""

#: includes/class-cfb-variables.php:95
msgid "Inactive"
msgstr ""

#: includes/class-cfb-variables.php:100
msgid "Edit"
msgstr ""

#: includes/class-cfb-variables.php:103
msgid "Deactivate"
msgstr ""

#: includes/class-cfb-variables.php:103
msgid "Activate"
msgstr ""

#: includes/class-cfb-variables.php:106
msgid "Delete"
msgstr ""

#: includes/class-cfb-variables.php:125
msgid "Variable Name"
msgstr ""

#: includes/class-cfb-variables.php:130
msgid "Display Label"
msgstr ""

#: includes/class-cfb-variables.php:135
msgid "Default Value"
msgstr ""

#: includes/class-cfb-variables.php:140
msgid "Description"
msgstr ""

#: includes/class-cfb-variables.php:145
msgid "Category"
msgstr ""

#: includes/class-cfb-variables.php:155
msgid "Icon"
msgstr ""

#: includes/class-cfb-variables.php:160
msgid "Color"
msgstr ""

#: includes/class-cfb-variables.php:165
msgid "Save Variable"
msgstr ""

#: includes/class-cfb-variables.php:166
msgid "Cancel"
msgstr ""

# PDF Generator strings
#: includes/class-cfb-pdf-generator.php:85
msgid "PDF generation failed"
msgstr ""

#: includes/class-cfb-pdf-generator.php:95
msgid "Invoice not found"
msgstr ""

#: includes/class-cfb-pdf-generator.php:105
msgid "PDF generated successfully"
msgstr ""

# AI Settings strings
#: admin/views/ai-settings-redesigned.php:65
msgid "CFB Calculator - AI Settings & Formula Builder"
msgstr ""

#: admin/views/ai-settings-redesigned.php:70
msgid "AI Configuration"
msgstr ""

#: admin/views/ai-settings-redesigned.php:71
msgid "Formula Builder"
msgstr ""

#: admin/views/ai-settings-redesigned.php:72
msgid "Formula Tester"
msgstr ""

#: admin/views/ai-settings-redesigned.php:73
msgid "Variables Manager"
msgstr ""

#: admin/views/ai-settings-redesigned.php:74
msgid "Debugging Tools"
msgstr ""

#: admin/views/ai-settings-redesigned.php:83
msgid "AI Configuration"
msgstr ""

#: admin/views/ai-settings-redesigned.php:87
msgid "Enable AI Features"
msgstr ""

#: admin/views/ai-settings-redesigned.php:91
msgid "Enable AI-powered formula suggestions and optimization"
msgstr ""

#: admin/views/ai-settings-redesigned.php:97
msgid "API Key"
msgstr ""

#: admin/views/ai-settings-redesigned.php:100
msgid "Enter your OpenAI API key for AI features"
msgstr ""

#: admin/views/ai-settings-redesigned.php:105
msgid "AI Model"
msgstr ""

#: admin/views/ai-settings-redesigned.php:116
msgid "Temperature"
msgstr ""

#: admin/views/ai-settings-redesigned.php:120
msgid "Controls randomness in AI responses (0 = deterministic, 1 = creative)"
msgstr ""

#: admin/views/ai-settings-redesigned.php:125
msgid "Max Tokens"
msgstr ""

#: admin/views/ai-settings-redesigned.php:128
msgid "Maximum tokens for AI responses"
msgstr ""

#: admin/views/ai-settings-redesigned.php:133
msgid "Formula Suggestions"
msgstr ""

#: admin/views/ai-settings-redesigned.php:137
msgid "Enable AI formula suggestions in form builder"
msgstr ""

#: admin/views/ai-settings-redesigned.php:143
msgid "Auto Optimization"
msgstr ""

#: admin/views/ai-settings-redesigned.php:147
msgid "Automatically optimize formulas for better performance"
msgstr ""

#: admin/views/ai-settings-redesigned.php:158
msgid "Advanced Formula Builder"
msgstr ""

#: admin/views/ai-settings-redesigned.php:162
msgid "Formula Input"
msgstr ""

#: admin/views/ai-settings-redesigned.php:179
msgid "Available Variables"
msgstr ""

#: admin/views/ai-settings-redesigned.php:182
msgid "Form Fields (Use these in formulas)"
msgstr ""

#: admin/views/ai-settings-redesigned.php:211
msgid "Global Variables (For reference only)"
msgstr ""

#: admin/views/ai-settings-redesigned.php:232
msgid "Formula Preview"
msgstr ""

#: admin/views/ai-settings-redesigned.php:244
msgid "Formula Tester"
msgstr ""

#: admin/views/ai-settings-redesigned.php:248
msgid "Test Formula"
msgstr ""

#: admin/views/ai-settings-redesigned.php:251
msgid "Test Variables"
msgstr ""

#: admin/views/ai-settings-redesigned.php:258
msgid "Test Formula"
msgstr ""

#: admin/views/ai-settings-redesigned.php:263
msgid "Test Result"
msgstr ""

#: admin/views/ai-settings-redesigned.php:275
msgid "Formula Examples"
msgstr ""

#: admin/views/ai-settings-redesigned.php:278
msgid "Basic Calculation"
msgstr ""

#: admin/views/ai-settings-redesigned.php:280
msgid "Simple multiplication"
msgstr ""

#: admin/views/ai-settings-redesigned.php:283
msgid "With Tax"
msgstr ""

#: admin/views/ai-settings-redesigned.php:285
msgid "Price with tax calculation"
msgstr ""

#: admin/views/ai-settings-redesigned.php:288
msgid "Tiered Pricing"
msgstr ""

#: admin/views/ai-settings-redesigned.php:290
msgid "Complex tiered pricing formula"
msgstr ""

#: admin/views/ai-settings-redesigned.php:293
msgid "Discount Calculation"
msgstr ""

#: admin/views/ai-settings-redesigned.php:295
msgid "Price with maximum 50% discount"
msgstr ""

#: admin/views/ai-settings-redesigned.php:305
msgid "Variables Manager"
msgstr ""

#: admin/views/ai-settings-redesigned.php:309
msgid "Add New Variable"
msgstr ""

#: admin/views/ai-settings-redesigned.php:314
msgid "Add Variable"
msgstr ""

#: admin/views/ai-settings-redesigned.php:319
msgid "Existing Variables"
msgstr ""

#: admin/views/ai-settings-redesigned.php:324
msgid "Variable Name"
msgstr ""

#: admin/views/ai-settings-redesigned.php:325
msgid "Label"
msgstr ""

#: admin/views/ai-settings-redesigned.php:326
msgid "Value"
msgstr ""

#: admin/views/ai-settings-redesigned.php:327
msgid "Status"
msgstr ""

#: admin/views/ai-settings-redesigned.php:328
msgid "Actions"
msgstr ""

#: admin/views/ai-settings-redesigned.php:341
msgid "Active"
msgstr ""

#: admin/views/ai-settings-redesigned.php:341
msgid "Inactive"
msgstr ""

#: admin/views/ai-settings-redesigned.php:343
msgid "Deactivate"
msgstr ""

#: admin/views/ai-settings-redesigned.php:343
msgid "Activate"
msgstr ""

#: admin/views/ai-settings-redesigned.php:344
msgid "Delete"
msgstr ""

#: admin/views/ai-settings-redesigned.php:363
msgid "Debugging Tools"
msgstr ""

#: admin/views/ai-settings-redesigned.php:367
msgid "Formula Debugger"
msgstr ""

#: admin/views/ai-settings-redesigned.php:371
msgid "Debug Formula"
msgstr ""

#: admin/views/ai-settings-redesigned.php:377
msgid "System Information"
msgstr ""

#: admin/views/ai-settings-redesigned.php:380
msgid "PHP Version:"
msgstr ""

#: admin/views/ai-settings-redesigned.php:383
msgid "WordPress Version:"
msgstr ""

#: admin/views/ai-settings-redesigned.php:386
msgid "CFB Plugin Version:"
msgstr ""

#: admin/views/ai-settings-redesigned.php:389
msgid "Available Functions:"
msgstr ""

#: admin/views/ai-settings-redesigned.php:403
msgid "Database Tables:"
msgstr ""

#: admin/views/ai-settings-redesigned.php:422
msgid "Recent Error Logs"
msgstr ""

#: admin/views/ai-settings-redesigned.php:458
msgid "Quick Tests"
msgstr ""

#: admin/views/ai-settings-redesigned.php:460
msgid "Test Basic Math"
msgstr ""

#: admin/views/ai-settings-redesigned.php:461
msgid "Test Functions"
msgstr ""

#: admin/views/ai-settings-redesigned.php:462
msgid "Test Variables"
msgstr ""

#: admin/views/ai-settings-redesigned.php:463
msgid "Clear Output"
msgstr ""

#: admin/views/ai-settings-redesigned.php:472
msgid "Save AI Settings"
msgstr ""

# PDF & Invoice Settings
#: includes/class-cfb-settings.php:376
msgid "PDF & Invoice Settings"
msgstr ""

#: includes/class-cfb-settings.php:382
msgid "Company Information"
msgstr ""

#: includes/class-cfb-settings.php:396
msgid "Company name to appear on invoices"
msgstr ""

#: includes/class-cfb-settings.php:401
msgid "Company Email"
msgstr ""

#: includes/class-cfb-settings.php:409
msgid "Company email address"
msgstr ""

#: includes/class-cfb-settings.php:414
msgid "Company Phone"
msgstr ""

#: includes/class-cfb-settings.php:421
msgid "Company phone number"
msgstr ""

#: includes/class-cfb-settings.php:426
msgid "Company Website"
msgstr ""

#: includes/class-cfb-settings.php:433
msgid "Company website URL"
msgstr ""

#: includes/class-cfb-settings.php:439
msgid "Company Address"
msgstr ""

#: includes/class-cfb-settings.php:445
msgid "Full company address for invoices"
msgstr ""

#: includes/class-cfb-settings.php:452
msgid "Company Logo"
msgstr ""

#: includes/class-cfb-settings.php:463
msgid "No logo uploaded"
msgstr ""

#: includes/class-cfb-settings.php:470
msgid "Upload Logo"
msgstr ""

#: includes/class-cfb-settings.php:475
msgid "Remove Logo"
msgstr ""

#: includes/class-cfb-settings.php:481
msgid "Upload your company logo for invoices. Recommended size: 200x80px. Supported formats: JPG, PNG, SVG"
msgstr ""

#: includes/class-cfb-settings.php:491
msgid "PDF Design & Layout"
msgstr ""

#: includes/class-cfb-settings.php:524
msgid "Choose the PDF template design"
msgstr ""

#: includes/class-cfb-settings.php:531
msgid "Font Family"
msgstr ""

#: includes/class-cfb-settings.php:546
msgid "Persian Fonts"
msgstr ""

#: includes/class-cfb-settings.php:562
msgid "Font family for PDF content"
msgstr ""

#: includes/class-cfb-settings.php:585
msgid "%d Persian fonts installed"
msgstr ""

#: includes/class-cfb-settings.php:589
msgid "No Persian fonts installed"
msgstr ""

#: includes/class-cfb-settings.php:591
msgid "Install Persian fonts for better RTL text support."
msgstr ""

#: includes/class-cfb-settings.php:592
msgid "Install fonts"
msgstr ""

#: includes/class-cfb-settings.php:601
msgid "Font Size"
msgstr ""

#: includes/class-cfb-settings.php:610
msgid "Base font size for PDF content"
msgstr ""

#: includes/class-cfb-settings.php:615
msgid "Color Scheme"
msgstr ""

#: includes/class-cfb-settings.php:619
msgid "Professional Blue"
msgstr ""

#: includes/class-cfb-settings.php:622
msgid "Business Green"
msgstr ""

#: includes/class-cfb-settings.php:625
msgid "Corporate Gray"
msgstr ""

#: includes/class-cfb-settings.php:628
msgid "Custom Colors"
msgstr ""

#: includes/class-cfb-settings.php:631
msgid "Color scheme for PDF design"
msgstr ""

#: includes/class-cfb-settings.php:642
msgid "RTL Support"
msgstr ""

#: includes/class-cfb-settings.php:644
msgid "Enable Right-to-Left layout for Arabic, Hebrew, Farsi languages"
msgstr ""

#: includes/class-cfb-settings.php:655
msgid "Convert to Farsi Digits"
msgstr ""

#: includes/class-cfb-settings.php:657
msgid "Convert all numbers to Farsi/Persian digits (۰۱۲۳۴۵۶۷۸۹)"
msgstr ""

#: includes/class-cfb-settings.php:666
msgid "Invoice Configuration"
msgstr ""

#: includes/class-cfb-settings.php:672
msgid "Invoice Title"
msgstr ""

#: includes/class-cfb-settings.php:680
msgid "Title text to display on invoices (e.g., INVOICE, فاکتور, BILL)"
msgstr ""

#: includes/class-cfb-settings.php:685
msgid "Invoice Number Prefix"
msgstr ""

#: includes/class-cfb-settings.php:693
msgid "Prefix for invoice numbers (e.g., INV-202401-0001)"
msgstr ""

#: includes/class-cfb-settings.php:699
msgid "Payment Terms"
msgstr ""

#: includes/class-cfb-settings.php:705
msgid "Default payment terms to appear on invoices"
msgstr ""

#: includes/class-cfb-settings.php:1128
msgid "Media uploader not available. Please refresh the page."
msgstr ""

#: includes/class-cfb-settings.php:1141
msgid "Choose Company Logo"
msgstr ""

#: includes/class-cfb-settings.php:1142
msgid "Use this logo"
msgstr ""

#: includes/class-cfb-settings.php:1184
msgid "Are you sure you want to remove the logo?"
msgstr ""

#: includes/class-cfb-settings.php:1223
msgid "This field is required"
msgstr ""

# Admin messages
#: includes/class-cfb-admin.php:330
msgid "Form saved successfully!"
msgstr ""

#: includes/class-cfb-admin.php:331
msgid "Form deleted successfully!"
msgstr ""

#: includes/class-cfb-admin.php:332
msgid "Form duplicated successfully!"
msgstr ""

# Frontend messages
msgid "Calculation failed"
msgstr ""

msgid "Network error occurred"
msgstr ""

msgid "Loading..."
msgstr ""

msgid "Error occurred"
msgstr ""

msgid "Please fill in all required fields"
msgstr ""

msgid "Invalid input"
msgstr ""

msgid "Calculation in progress..."
msgstr ""

msgid "Results"
msgstr ""

msgid "Breakdown"
msgstr ""

msgid "Subtotal"
msgstr ""

msgid "Grand Total"
msgstr ""

msgid "Want Invoice?"
msgstr ""

msgid "Generate Invoice"
msgstr ""

msgid "Invoice Details"
msgstr ""

msgid "Name"
msgstr ""

msgid "Email"
msgstr ""

msgid "Phone"
msgstr ""

msgid "Company"
msgstr ""

msgid "Notes"
msgstr ""
