<?php
/**
 * Debug Field Labels in PDF
 */

require_once('../../../wp-config.php');
global $wpdb;

// Get the latest invoice we just created
$invoice = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}cfb_invoices ORDER BY id DESC LIMIT 1");

echo "=== DEBUGGING PDF FIELD LABELS ===\n";
echo "Invoice ID: {$invoice->id}\n";
echo "Form ID: {$invoice->form_id}\n";
echo "Submission ID: {$invoice->submission_id}\n";

// Check what form_data is in the invoice
echo "\n=== INVOICE FORM DATA ===\n";
if ($invoice->form_data) {
    $invoice_form_data = json_decode($invoice->form_data, true);
    echo "Invoice form_data keys: " . implode(', ', array_keys($invoice_form_data)) . "\n";
    foreach ($invoice_form_data as $key => $value) {
        echo "{$key}: {$value}\n";
    }
} else {
    echo "NO form_data in invoice\n";
}

// Check what's in the submission
echo "\n=== SUBMISSION DATA ===\n";
if ($invoice->submission_id) {
    $submission = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$wpdb->prefix}cfb_form_submissions WHERE id = %d", $invoice->submission_id));
    if ($submission && $submission->submission_data) {
        $submission_data = json_decode($submission->submission_data, true);
        echo "Submission data keys: " . implode(', ', array_keys($submission_data)) . "\n";
        foreach ($submission_data as $key => $value) {
            echo "{$key}: {$value}\n";
        }
    }
}

// Test the field labels method
echo "\n=== FIELD LABELS TEST ===\n";
if (class_exists('CFB_PDF_Generator')) {
    $pdf_generator = CFB_PDF_Generator::get_instance();
    $reflection = new ReflectionClass($pdf_generator);
    $method = $reflection->getMethod('get_form_field_labels');
    $method->setAccessible(true);
    $field_labels = $method->invoke($pdf_generator, $invoice->form_id);
    
    echo "Field labels found: " . count($field_labels) . "\n";
    foreach ($field_labels as $name => $label) {
        echo "{$name} => {$label}\n";
    }
} else {
    echo "CFB_PDF_Generator class not found\n";
    exit;
}

// Test what data is actually used in PDF
echo "\n=== PDF FORM DATA RETRIEVAL TEST ===\n";
$form_data = array();

// Method 1: From invoice form_data
if (isset($invoice->form_data) && !empty($invoice->form_data)) {
    $form_data = json_decode($invoice->form_data, true);
    echo "Method 1 (invoice form_data): " . count($form_data) . " fields\n";
}

// Method 2: From submission
if (empty($form_data) && isset($invoice->submission_id) && $invoice->submission_id) {
    $submission = $wpdb->get_row($wpdb->prepare("SELECT submission_data FROM {$wpdb->prefix}cfb_form_submissions WHERE id = %d", $invoice->submission_id));
    if ($submission && $submission->submission_data) {
        $form_data = json_decode($submission->submission_data, true);
        echo "Method 2 (submission data): " . count($form_data) . " fields\n";
    }
}

echo "\nFinal form_data for PDF:\n";
foreach ($form_data as $field_name => $field_value) {
    if (in_array($field_name, array('nonce', 'action', '_wp_http_referer', 'customer_name', 'customer_email', 'customer_phone', 'customer_address', 'invoice_name', 'invoice_email', 'invoice_phone', 'invoice_address'))) {
        echo "SKIPPED: {$field_name} => {$field_value}\n";
        continue;
    }
    
    $clean_field_name = isset($field_labels[$field_name]) ? $field_labels[$field_name] : 'NO LABEL';
    echo "{$field_name} => {$field_value} (Label: {$clean_field_name})\n";
}

echo "\n=== CONCLUSION ===\n";
echo "If labels show 'NO LABEL', that's why PDF shows empty field labels.\n";
echo "The field names in form_data must match the field names in form definition.\n";

?>
